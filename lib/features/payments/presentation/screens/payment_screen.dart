import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/core/widgets/loading_indicator.dart';
import 'package:gaadi_sewa/core/widgets/primary_button.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/payments/presentation/widgets/payment_method_selector.dart';

class PaymentScreen extends ConsumerStatefulWidget {
  final BookingModel booking;

  const PaymentScreen({
    Key? key,
    required this.booking,
  }) : super(key: key);

  @override
  ConsumerState<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends ConsumerState<PaymentScreen> {
  String _selectedMethod = 'khalti';
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Payment'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking Summary
            _buildBookingSummary(),
            const SizedBox(height: 24),
            
            // Payment Methods
            PaymentMethodSelector(
              selectedMethod: _selectedMethod,
              onMethodSelected: (method) {
                setState(() {
                  _selectedMethod = method;
                });
              },
            ),
            const SizedBox(height: 32),
            
            // Terms and Conditions
            _buildTermsAndConditions(),
            const SizedBox(height: 24),
            
            // Pay Now Button
            _buildPayNowButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingSummary() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Booking Summary',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Vehicle', '${widget.booking.vehicle.make} ${widget.booking.vehicle.model}'),
            _buildSummaryRow('Rental Period', '${widget.booking.startDate.toString().split(' ')[0]} - ${widget.booking.endDate.toString().split(' ')[0]}'),
            _buildSummaryRow('Daily Rate', 'NPR ${widget.booking.vehicle.dailyRate.toStringAsFixed(2)}'),
            const Divider(height: 32),
            _buildSummaryRow(
              'Total Amount',
              'NPR ${widget.booking.totalAmount.toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Terms & Conditions',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8),
        Text(
          '• By proceeding, you agree to our Terms of Service and Privacy Policy.\n'
          '• A security deposit may be required by the vehicle owner.\n'
          '• Cancellation policy: Free cancellation up to 24 hours before pickup.\n'
          '• In case of any issues, please contact our support team.',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildPayNowButton() {
    return _isProcessing
        ? const Center(child: LoadingIndicator())
        : PrimaryButton(
            text: 'Pay NPR ${widget.booking.totalAmount.toStringAsFixed(2)}',
            onPressed: _processPayment,
          );
  }

  Future<void> _processPayment() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // TODO: Implement actual payment processing
      // 1. Call payment gateway API
      // 2. Handle payment response
      // 3. Navigate to success/failure screen
      
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        Navigator.pushReplacementNamed(
          context, 
          '/payment/success',
          arguments: {
            'booking': widget.booking,
            'paymentMethod': _selectedMethod,
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Payment failed: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
